# Database Configuration
DATABASE_URL=your_database_connection_string

# Email Configuration (Required for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Admin Configuration (Required for admin notifications)
ADMIN_EMAIL=<EMAIL>

# Auto-Deployment Configuration (Required for automatic rebuilds)
VERCEL_DEPLOY_HOOK_URL=https://api.vercel.com/v1/integrations/deploy/xxx/yyy

# OpenRouter API (for verse generation)
OPENROUTER_API_KEY=your_openrouter_api_key

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Deployment Configuration
VERCEL_URL=your_deployment_url

name: Generate Daily Verse

on:
  schedule:
    # Runs every day at 6:00 AM UTC (which is 9:00 AM Saudi time)
    - cron: '15 22 * * *'
  workflow_dispatch: # Allows manual triggering

jobs:
  generate-verse:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm install

      - name: Run daily verse generation
        run: curl -X POST https://quran-verses-gilt.vercel.app/api/cron/daily-verse
        env:
          VERCEL_URL: quran-verses-gilt.vercel.app
          OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}

      - name: Check for errors
        if: failure()
        run: |
          echo "Daily verse generation failed!"
          exit 1

name: Scheduled Tasks

on:
  schedule:
    # Daily reminder at 22:34 UTC
    - cron: '34 22 * * *'
    # Weekly reminder on Sunday at 22:30 UTC
    - cron: '30 22 * * 0'
    # Daily revalidation at 00:00 UTC
    - cron: '0 0 * * *'
  workflow_dispatch: # Allow manual triggering

jobs:
  run-tasks:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Debug schedule event
        if: github.event_name == 'schedule'
        run: |
          echo "Schedule triggered: ${{ github.event.schedule }}"
          echo "Event name: ${{ github.event_name }}"
          echo "Current time: $(date -u)"

      - name: Run daily email task
        if: github.event_name == 'schedule' && github.event.schedule == '34 22 * * *'
        id: daily-email
        run: |
          curl -X POST "${{ secrets.VERCEL_URL }}/api/email/notify" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -d '{"type": "daily_reminder"}'

      - name: Run weekly email task
        if: github.event_name == 'schedule' && github.event.schedule == '30 22 * * 0'
        id: weekly-email
        run: |
          curl -X POST "${{ secrets.VERCEL_URL }}/api/email/notify" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -d '{"type": "weekly_reminder"}'

      - name: Run revalidation task
        if: github.event_name == 'schedule' && github.event.schedule == '0 0 * * *'
        id: revalidation
        run: |
          curl -X GET "${{ secrets.VERCEL_URL }}/api/cron/revalidate" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}"

      - name: Notify on failure
        if: failure()
        run: |
          FAILED_STEPS=""
          if [ "${{ steps.daily-email.outcome }}" = "failure" ]; then
            FAILED_STEPS="${FAILED_STEPS}Daily Email, "
          fi
          if [ "${{ steps.weekly-email.outcome }}" = "failure" ]; then
            FAILED_STEPS="${FAILED_STEPS}Weekly Email, "
          fi
          if [ "${{ steps.revalidation.outcome }}" = "failure" ]; then
            FAILED_STEPS="${FAILED_STEPS}Revalidation, "
          fi
          FAILED_STEPS=$(echo "$FAILED_STEPS" | sed 's/, $//')

          curl -X POST "${{ secrets.SLACK_WEBHOOK_URL }}" \
            -H 'Content-type: application/json' \
            --data "{
              \"text\": \"GitHub Actions scheduled task failed\",
              \"blocks\": [
                {
                  \"type\": \"header\",
                  \"text\": {
                    \"type\": \"plain_text\",
                    \"text\": \"GitHub Actions Scheduled Task Failed\"
                  }
                },
                {
                  \"type\": \"section\",
                  \"text\": {
                    \"type\": \"mrkdwn\",
                    \"text\": \"*Repository:* ${{ github.repository }}\\n*Workflow:* ${{ github.workflow }}\\n*Run Number:* ${{ github.run_number }}\\n*Run Attempt:* ${{ github.run_attempt }}\\n*Event:* ${{ github.event_name }}\\n*Ref:* ${{ github.ref }}\\n*SHA:* ${{ github.sha }}\\n*Failed Steps:* ${FAILED_STEPS:-None}\"
                  }
                },
                {
                  \"type\": \"actions\",
                  \"elements\": [
                    {
                      \"type\": \"button\",
                      \"text\": {
                        \"type\": \"plain_text\",
                        \"text\": \"View Run Details\"
                      },
                      \"url\": \"${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}\"
                    }
                  ]
                }
              ]
            }"
        continue-on-error: true

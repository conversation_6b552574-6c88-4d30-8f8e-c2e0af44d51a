import { NextRequest, NextResponse } from 'next/server'
import { supabaseVerseManager } from '@/lib/supabase'
import { NotificationManager } from '@/lib/notification-utils'
import { withAdminAuth } from '@/lib/admin-auth'

// Get all pending verses for admin review
export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const pendingVerses = await supabaseVerseManager.getVerses('pending')

    return NextResponse.json({
      success: true,
      verses: pendingVerses,
    })
  } catch (error) {
    console.error('Error fetching pending verses:', error)
    return NextResponse.json(
      { success: false, message: 'فشل في جلب الآيات المعلقة' },
      { status: 500 }
    )
  }
})

// Approve or reject a verse
export const POST = withAdminAuth(async (request: NextRequest) => {
  let verseId = 'unknown'
  let action = 'unknown'

  try {
    const requestData = await request.json()
    verseId = requestData.verseId
    action = requestData.action

    if (!verseId || !['approve', 'reject'].includes(action)) {
      return NextResponse.json({ success: false, message: 'بيانات غير صالحة' }, { status: 400 })
    }

    // Get the verse first to check if it exists
    const verse = await supabaseVerseManager.getVerseBySlug(verseId) // Assuming verseId is actually slug
    if (!verse) {
      console.error('Verse not found:', verseId)
      return NextResponse.json({ success: false, message: 'الآية غير موجودة' }, { status: 404 })
    }

    if (action === 'approve') {
      // Update verse status to published
      const success = await supabaseVerseManager.updateVerseStatus(verse.id, 'published')

      if (!success) {
        throw new Error('Failed to update verse status')
      }

      // Trigger deployment webhook for auto-rebuild
      try {
        let baseUrl = 'http://localhost:3000' // Default for local development

        // Production URL detection
        if (process.env.VERCEL_URL) {
          // VERCEL_URL doesn't include protocol and is the actual deployment URL
          baseUrl = `https://${process.env.VERCEL_URL}`
        } else if (process.env.NEXTAUTH_URL && !process.env.NEXTAUTH_URL.includes('localhost')) {
          // NEXTAUTH_URL already includes protocol, use it if not localhost
          baseUrl = process.env.NEXTAUTH_URL.replace(/\/$/, '') // Remove trailing slash
        } else if (process.env.NODE_ENV === 'production') {
          // Fallback for production - skip webhook in production if no URL is configured
          console.log('⚠️ Production deployment webhook skipped - no deployment URL configured')
          return NextResponse.json({
            success: true,
            message: 'تم نشر الآية بنجاح',
          })
        }

        const webhookUrl = `${baseUrl}/api/webhook/deploy`
        console.log('🔗 Triggering deployment webhook at:', webhookUrl)

        // Prepare headers with Vercel bypass token if available
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
        }

        // Add Vercel deployment protection bypass token if available
        if (process.env.VERCEL_AUTOMATION_BYPASS_SECRET) {
          headers['x-vercel-protection-bypass'] = process.env.VERCEL_AUTOMATION_BYPASS_SECRET
          console.log('🔑 Added Vercel bypass token to webhook request')
        } else {
          console.warn('⚠️ VERCEL_AUTOMATION_BYPASS_SECRET not found - webhook may fail with 401')
        }

        const response = await fetch(webhookUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            action: 'verse_approved',
            verseId: verse.id,
            slug: verse.slug,
          }),
        })

        if (response.ok) {
          console.log('✅ Deployment webhook triggered successfully for:', verse.slug)
        } else {
          console.log('⚠️ Deployment webhook returned non-200 status:', response.status)
        }
      } catch (webhookError) {
        console.error('⚠️ Failed to trigger deployment webhook:', webhookError)
        // Don't fail the approval if webhook fails
      }

      return NextResponse.json({
        success: true,
        message: 'تم نشر الآية بنجاح وسيتم تحديث الموقع قريباً',
      })
    } else if (action === 'reject') {
      // Delete the verse
      const success = await supabaseVerseManager.deleteVerse(verse.id)

      if (!success) {
        throw new Error('Failed to delete verse')
      }

      console.log('✅ Verse rejected and deleted:', verse.slug)

      return NextResponse.json({
        success: true,
        message: 'تم رفض الآية وحذفها',
      })
    }
  } catch (error) {
    console.error('❌ Error processing verse:', error)
    console.error('❌ Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      verseId: verseId || 'Unknown verse',
    })

    return NextResponse.json(
      {
        success: false,
        message: 'فشل في معالجة الآية - يرجى المحاولة مرة أخرى أو التواصل مع المطور',
      },
      { status: 500 }
    )
  }
})

import { NextRequest, NextResponse } from 'next/server'
import { supabaseSubscriptionManager } from '@/lib/supabase'
import { NotificationManager } from '@/lib/notification-utils'
import { redirect } from 'next/navigation'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return redirect('/email/confirmed?error=missing_token')
    }

    const result = await supabaseSubscriptionManager.confirmSubscriptionWithDetails(token)

    // Log for debugging in production
    console.log('Confirmation attempt:', {
      tokenPresent: !!token,
      found: result.found,
      alreadyConfirmed: result.alreadyConfirmed,
      success: result.success,
    })

    if (!result.found) {
      if (result.alreadyConfirmed) {
        // There are confirmed subscriptions, so this token was likely already used
        console.log('Redirecting to already confirmed page')
        return NextResponse.redirect(
          new URL('/email/confirmed?success=true&already_confirmed=true', request.url)
        )
      } else {
        // No confirmed subscriptions found, this is likely an invalid token
        console.log('Invalid token - no confirmed subscriptions found')
        return NextResponse.json(
          { error: 'Invalid or expired confirmation token' },
          { status: 404 }
        )
      }
    }

    if (result.alreadyConfirmed) {
      // Subscription was already confirmed
      console.log('Subscription already confirmed, redirecting')
      return NextResponse.redirect(
        new URL('/email/confirmed?success=true&already_confirmed=true', request.url)
      )
    }

    if (result.success) {
      // Confirmation was successful
      console.log('Confirmation successful')

      // Send admin notification for new subscriber
      try {
        if (result.subscription) {
          const notificationManager = new NotificationManager()
          await notificationManager.sendNewSubscriberNotification(
            result.subscription.email,
            result.subscription.frequency,
            result.subscription.language
          )
        }
      } catch (notificationError) {
        console.error('Failed to send admin notification for new subscriber:', notificationError)
        // Don't fail the confirmation if notification fails
      }

      return NextResponse.redirect(new URL('/email/confirmed?success=true', request.url))
    } else {
      // Confirmation failed
      console.log('Confirmation failed')
      return NextResponse.json({ error: 'Failed to confirm subscription' }, { status: 500 })
    }
  } catch (error) {
    console.error('Email confirmation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

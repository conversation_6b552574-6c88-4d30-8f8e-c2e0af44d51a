import { NextRequest, NextResponse } from 'next/server'
import { NotificationManager } from '@/lib/notification-utils'

// This endpoint should be called by cron jobs or webhooks
export async function POST(request: NextRequest) {
  try {
    // Verify the request is authorized (you might want to add API key authentication)
    const authHeader = request.headers.get('authorization')
    const expectedAuth =
      process.env.CRON_SECRET || process.env.NEXT_PUBLIC_CRON_SECRET || 'your-secret-key'

    if (authHeader !== `Bearer ${expectedAuth}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { type, verseSlug } = body

    const notificationManager = new NotificationManager()
    let result

    switch (type) {
      case 'new_verse': {
        if (!verseSlug) {
          return NextResponse.json(
            { error: 'Verse slug is required for new_verse notifications' },
            { status: 400 }
          )
        }
        result = await notificationManager.sendNewVerseNotifications(verseSlug)
        break
      }

      case 'daily_reminder': {
        result = await notificationManager.sendDailyReminders()
        break
      }

      case 'weekly_reminder': {
        result = await notificationManager.sendWeeklyReminders()
        break
      }

      default:
        return NextResponse.json(
          {
            error:
              'Invalid notification type. Must be: new_verse, daily_reminder, or weekly_reminder',
          },
          { status: 400 }
        )
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to send notifications' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Email notifications processed',
      emailType: type,
      ...result,
    })
  } catch (error) {
    console.error('Email notification error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// GET endpoint to check notification status or get stats
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    const notificationManager = new NotificationManager()

    switch (action) {
      case 'stats': {
        const stats = await notificationManager.getNotificationStats()
        return NextResponse.json({
          stats,
          message: 'Email notification system is operational',
        })
      }

      case 'logs': {
        const logs = await notificationManager.getRecentEmailLogs(50)
        return NextResponse.json({
          logs,
          message: 'Recent email logs retrieved',
        })
      }

      case 'test': {
        // Test endpoint to verify the system is working
        const { getTodaysVerse } = await import('@/lib/verses')
        const todaysVerse = getTodaysVerse()
        return NextResponse.json({
          message: 'Email notification system test',
          todaysVerse: todaysVerse
            ? {
                title: todaysVerse.title,
                slug: todaysVerse.slug,
              }
            : null,
          timestamp: new Date().toISOString(),
        })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: stats, logs, or test' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Email notification GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

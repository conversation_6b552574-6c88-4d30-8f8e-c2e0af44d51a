import { NextRequest, NextResponse } from 'next/server'
import { NotificationManager } from '@/lib/notification-utils'
import { supabaseVerseManager } from '@/lib/supabase'

// API configuration for OpenRouter
const OPENROUTER_API_KEY =
  process.env.OPENROUTER_API_KEY ||
  '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'
const MODEL_NAME = 'mistralai/mistral-medium-3.1'

// Validation function to ensure all required fields are present and valid
function validateVerseData(verseData: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check required string fields
  if (!verseData.title || verseData.title.trim() === '') {
    errors.push('العنوان مطلوب')
  }

  if (!verseData.surah || verseData.surah.trim() === '' || verseData.surah === 'سورة غير محددة') {
    errors.push('اسم السورة مطلوب ويجب أن يكون صحيحاً')
  }

  if (!verseData.ayah || verseData.ayah < 1 || isNaN(verseData.ayah)) {
    errors.push('رقم الآية مطلوب ويجب أن يكون رقماً صحيحاً أكبر من 0')
  }

  if (!verseData.slug || verseData.slug.trim() === '') {
    errors.push('معرف الآية (slug) مطلوب')
  }

  if (!verseData.verse_text || verseData.verse_text.trim() === '') {
    errors.push('نص الآية مطلوب')
  }

  if (
    !verseData.explanation ||
    verseData.explanation.trim() === '' ||
    verseData.explanation === 'شرح الآية...'
  ) {
    errors.push('شرح الآية مطلوب ويجب أن يكون محتوى حقيقي')
  }

  if (!verseData.practical_application || verseData.practical_application.trim() === '') {
    errors.push('التطبيق العملي مطلوب')
  }

  if (!verseData.reflection_questions || verseData.reflection_questions.trim() === '') {
    errors.push('أسئلة التأمل مطلوبة')
  }

  if (!verseData.date || verseData.date.trim() === '') {
    errors.push('تاريخ الآية مطلوب')
  }

  // Validate arrays
  if (!Array.isArray(verseData.tags) || verseData.tags.length === 0) {
    errors.push('العلامات مطلوبة ويجب أن تكون مصفوفة غير فارغة')
  }

  // Additional content quality checks
  if (verseData.explanation && verseData.explanation.length < 50) {
    errors.push('الشرح قصير جداً - يجب أن يكون أكثر تفصيلاً')
  }

  if (verseData.verse_text && verseData.verse_text.length < 10) {
    errors.push('نص الآية قصير جداً أو غير مكتمل')
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// Function to generate a verse article using the AI model
async function generateVerseArticle(): Promise<any> {
  const maxRetries = 3
  let attempt = 0

  while (attempt < maxRetries) {
    try {
      // Get today's date
      const today = new Date()
      const dateStr = today.toISOString().split('T')[0]

      // Generate a varied prompt to encourage different verse selection
      const prompt = generateVariedPrompt(attempt)

      // Call the OpenRouter API
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://quranverses.com',
          'X-Title': 'Quran Verses Explanation',
        },
        body: JSON.stringify({
          model: MODEL_NAME,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          max_tokens: 2000,
          temperature: 0.8 + attempt * 0.1, // Increase randomness with each retry
        }),
      })

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.statusText}`)
      }

      const data = await response.json()
      const aiResponse = data.choices[0].message.content

      console.log(`🤖 محاولة ${attempt + 1}: تم الحصول على استجابة من الذكاء الاصطناعي`)

      // Extract verse information and create article from AI response
      const extractedInfo = extractArticleInfoFromAI(aiResponse, dateStr)

      // Add the raw AI response for database storage
      extractedInfo.aiResponse = aiResponse

      // Validate the extracted information before checking for duplicates
      const tempVerseData = {
        title: extractedInfo.title,
        surah: extractedInfo.surah,
        ayah: extractedInfo.ayah,
        slug: extractedInfo.slug,
        verse_text: extractedInfo.verseText,
        explanation: extractExplanation(aiResponse),
        practical_application: extractPracticalApplications(aiResponse),
        reflection_questions: extractReflectionQuestions(aiResponse),
        tags: extractedInfo.tags,
        date: extractedInfo.date,
        status: 'pending',
      }

      const validation = validateVerseData(tempVerseData)
      if (!validation.isValid) {
        console.warn(
          `⚠️ فشل في التحقق من البيانات المستخرجة في المحاولة ${attempt + 1}:`,
          validation.errors
        )
        throw new Error(`بيانات غير صالحة: ${validation.errors.join(', ')}`)
      }

      console.log(`✅ محاولة ${attempt + 1}: تم استخراج البيانات والتحقق من صحتها بنجاح`)

      // Check if this slug already exists
      const existingVerse = await supabaseVerseManager.getVerseBySlug(extractedInfo.slug)
      if (existingVerse) {
        console.log(
          `⚠️  Slug ${extractedInfo.slug} already exists, retrying with different prompt (attempt ${attempt + 1}/${maxRetries})`
        )
        attempt++
        continue
      }

      return extractedInfo
    } catch (error) {
      console.error(`Error generating verse article (attempt ${attempt + 1}):`, error)
      if (attempt === maxRetries - 1) {
        throw error
      }
      attempt++
    }
  }

  throw new Error('Failed to generate unique verse after maximum retries')
}

// Function to generate varied prompts to encourage different verse selection
function generateVariedPrompt(attempt: number): string {
  const basePrompts = [
    // Original prompt
    `أكتب مقالًا تفسيريًا لآية من القرآن الكريم. اختر آية مناسبة وقدم شرحًا مفصلاً لها.`,

    // Focus on different themes
    `اختر آية من القرآن الكريم تتحدث عن الصبر أو التوكل أو الرحمة واكتب مقالًا تفسيريًا مفصلاً لها.`,

    // Focus on different surahs
    `اختر آية من إحدى السور المدنية في القرآن الكريم واكتب تفسيراً شاملاً لها مع التطبيق العملي.`,

    // Focus on practical guidance
    `اختر آية من القرآن الكريم تحتوي على توجيهات عملية للحياة اليومية واكتب شرحاً مفصلاً لها.`,

    // Focus on spiritual aspects
    `اختر آية من القرآن الكريم تتحدث عن العبادة أو الذكر أو التقوى واكتب تفسيراً روحانياً لها.`,
  ]

  const currentDate = new Date()
  const timeBasedVariation = [
    `في هذا اليوم المبارك، `,
    `مع بداية هذا الأسبوع، `,
    `في هذا الوقت من العام، `,
    `مع تقدم الأيام، `,
    ``,
  ]

  const randomVariation = [
    `يرجى اختيار آية مختلفة عن المواضيع السابقة. `,
    `ركز على آية لم يتم تفسيرها مؤخراً. `,
    `اختر آية من سورة مختلفة هذه المرة. `,
    `نوّع في اختيار الآية لتشمل مواضيع متنوعة. `,
    ``,
  ]

  const promptIndex = attempt % basePrompts.length
  const timeIndex = currentDate.getDay() % timeBasedVariation.length
  const randomIndex = Math.floor(Math.random() * randomVariation.length)

  const selectedPrompt = basePrompts[promptIndex]
  const timeVariation = timeBasedVariation[timeIndex]
  const randomText = randomVariation[randomIndex]

  return `${timeVariation}${randomText}${selectedPrompt}

المطلوب:
1. اختيار آية مناسبة من القرآن الكريم (آية مختلفة ومتنوعة)
2. ذكر اسم السورة ورقم الآية
3. كتابة نص الآية كاملاً
4. شرح مفصل للآية مع ذكر المعاني الأساسية
5. ثلاث نقاط تطبيقية عملية لحياة المسلم اليومي
6. ثلاث أسئلة للتأمل والتفكر

الرجاء الالتزام بالهيكل التالي:
**السورة**: [اسم السورة]
**الآية**: [رقم الآية]

## النص الكريم
[نص الآية هنا]

## الشرح والتفسير
[شرح مفصل للآية مع ذكر المعاني الأساسية]

## التطبيق العملي
### كيف نطبق هذا اليوم؟
1. **في المواقف اليومية**: [تطبيق عملي أول]
2. **في العبادات**: [تطبيق عملي ثاني]
3. **في العلاقات**: [تطبيق عملي ثالث]

## للتأمل والتفكر
- [سؤال للتأمل الأول]
- [سؤال للتأمل الثاني]
- [سؤال للتأمل الثالث]

يرجى أن يكون المحتوى غنيًا بالدلائل والمعاني، ويستخدم لغة واضحة وبليغة.`
}

// Function to convert Arabic surah names to English transliterations for URL-safe slugs
function transliterateSurahName(arabicName: string): string {
  const surahMapping: { [key: string]: string } = {
    البقرة: 'al-baqarah',
    الإخلاص: 'al-ikhlas',
    الرحمن: 'ar-rahman',
    الفاتحة: 'al-fatiha',
    النساء: 'an-nisa',
    المائدة: 'al-maidah',
    الأنعام: 'al-anam',
    الأعراف: 'al-araf',
    الأنفال: 'al-anfal',
    التوبة: 'at-tawbah',
    يونس: 'yunus',
    هود: 'hud',
    يوسف: 'yusuf',
    الرعد: 'ar-rad',
    إبراهيم: 'ibrahim',
    الحجر: 'al-hijr',
    النحل: 'an-nahl',
    الإسراء: 'al-isra',
    الكهف: 'al-kahf',
    مريم: 'maryam',
    طه: 'taha',
    الأنبياء: 'al-anbiya',
    الحج: 'al-hajj',
    المؤمنون: 'al-muminun',
    النور: 'an-nur',
    الفرقان: 'al-furqan',
    الشعراء: 'ash-shuara',
    النمل: 'an-naml',
    القصص: 'al-qasas',
    العنكبوت: 'al-ankabut',
    الروم: 'ar-rum',
    لقمان: 'luqman',
    السجدة: 'as-sajdah',
    الأحزاب: 'al-ahzab',
    سبأ: 'saba',
    فاطر: 'fatir',
    يس: 'yasin',
    الصافات: 'as-saffat',
    ص: 'sad',
    الزمر: 'az-zumar',
    غافر: 'ghafir',
    فصلت: 'fussilat',
    الشورى: 'ash-shura',
    الزخرف: 'az-zukhruf',
    الدخان: 'ad-dukhan',
    الجاثية: 'al-jathiyah',
    الأحقاف: 'al-ahqaf',
    محمد: 'muhammad',
    الفتح: 'al-fath',
    الحجرات: 'al-hujurat',
    ق: 'qaf',
    الذاريات: 'adh-dhariyat',
    الطور: 'at-tur',
    النجم: 'an-najm',
    القمر: 'al-qamar',
    الواقعة: 'al-waqiah',
    الحديد: 'al-hadid',
    المجادلة: 'al-mujadilah',
    الحشر: 'al-hashr',
    الممتحنة: 'al-mumtahanah',
    الصف: 'as-saff',
    الجمعة: 'al-jumuah',
    المنافقون: 'al-munafiqun',
    التغابن: 'at-taghabun',
    الطلاق: 'at-talaq',
    التحريم: 'at-tahrim',
    الملك: 'al-mulk',
    القلم: 'al-qalam',
    الحاقة: 'al-haqqah',
    المعارج: 'al-maarij',
    نوح: 'nuh',
    الجن: 'al-jinn',
    المزمل: 'al-muzzammil',
    المدثر: 'al-muddaththir',
    القيامة: 'al-qiyamah',
    الإنسان: 'al-insan',
    المرسلات: 'al-mursalat',
    النبأ: 'an-naba',
    النازعات: 'an-naziat',
    عبس: 'abasa',
    التكوير: 'at-takwir',
    الانفطار: 'al-infitar',
    المطففين: 'al-mutaffifin',
    الانشقاق: 'al-inshiqaq',
    البروج: 'al-buruj',
    الطارق: 'at-tariq',
    الأعلى: 'al-ala',
    الغاشية: 'al-ghashiyah',
    الفجر: 'al-fajr',
    البلد: 'al-balad',
    الشمس: 'ash-shams',
    الليل: 'al-layl',
    الضحى: 'ad-duha',
    الشرح: 'ash-sharh',
    التين: 'at-tin',
    العلق: 'al-alaq',
    القدر: 'al-qadr',
    البينة: 'al-bayyinah',
    الزلزلة: 'az-zalzalah',
    العاديات: 'al-adiyat',
    القارعة: 'al-qariah',
    التكاثر: 'at-takathur',
    العصر: 'al-asr',
    الهمزة: 'al-humazah',
    الفيل: 'al-fil',
    قريش: 'quraysh',
    الماعون: 'al-maun',
    الكوثر: 'al-kawthar',
    الكافرون: 'al-kafirun',
    النصر: 'an-nasr',
    المسد: 'al-masad',
    الفلق: 'al-falaq',
    الناس: 'an-nas',
  }

  return surahMapping[arabicName] || arabicName.toLowerCase().replace(/\s+/g, '-')
}

// Function to extract article information from AI response
function extractArticleInfoFromAI(aiResponse: string, dateStr: string): any {
  // Extract surah and ayah from AI response with multiple pattern attempts
  let surahMatch = aiResponse.match(/\*\*السورة\*\*:\s*(.+?)(\n|$)/)
  if (!surahMatch) {
    surahMatch = aiResponse.match(/السورة:\s*(.+?)(\n|$)/)
  }
  if (!surahMatch) {
    surahMatch = aiResponse.match(/سورة\s+(.+?)(\n|$)/)
  }

  let ayahMatch = aiResponse.match(/\*\*الآية\*\*:\s*(\d+)/)
  if (!ayahMatch) {
    ayahMatch = aiResponse.match(/الآية:\s*(\d+)/)
  }
  if (!ayahMatch) {
    ayahMatch = aiResponse.match(/آية\s*(\d+)/)
  }

  const rawSurah = surahMatch ? surahMatch[1].trim().replace(/[^\u0600-\u06FF\s]/g, '') : ''
  // Clean the surah name by removing "سورة" prefix if present
  const surah = rawSurah.replace(/^سورة\s*/, '').trim()
  const ayah = ayahMatch ? parseInt(ayahMatch[1]) : 0

  // Validate extracted surah and ayah
  if (!surah || surah === '') {
    throw new Error('فشل في استخراج اسم السورة من الاستجابة')
  }

  if (!ayah || ayah < 1) {
    throw new Error('فشل في استخراج رقم الآية من الاستجابة أو الرقم غير صحيح')
  }

  // Create a URL-safe slug using English transliteration
  // Add a timestamp suffix to ensure uniqueness
  const baseSlug = `${transliterateSurahName(surah)}-${ayah}`
  const timestamp = Date.now().toString().slice(-6) // Last 6 digits of timestamp
  const slug = `${baseSlug}-${timestamp}`

  // Extract verse text from AI response with multiple pattern attempts
  let verseTextMatch = aiResponse.match(/## النص الكريم\n([\s\S]*?)(?=\n## )/)
  if (!verseTextMatch) {
    verseTextMatch = aiResponse.match(/النص الكريم[:\n]\s*([\s\S]*?)(?=\n#|$)/)
  }
  if (!verseTextMatch) {
    verseTextMatch = aiResponse.match(/["""]([^"""]+)["""]/)
  }

  const verseText = verseTextMatch ? verseTextMatch[1].trim() : ''

  if (!verseText || verseText === '') {
    throw new Error('فشل في استخراج نص الآية من الاستجابة')
  }

  // Extract tags from the verse themes
  const tags = extractTags(aiResponse)

  return {
    title: `آية من سورة ${surah} (آية ${ayah})`,
    surah: surah,
    ayah: ayah,
    slug: slug,
    date: dateStr,
    tags: tags,
    verseText: verseText,
    aiResponse: '', // This will be filled by the calling function
    status: 'pending', // pending, approved, published
  }
}

// Helper functions to extract specific parts from AI response
function extractTags(aiResponse: string): string[] {
  // Extract tags from the explanation
  const commonTags = ['إيمان', 'توكل', 'تأمل', 'تدبر', 'عبادة', 'تزكية', 'ذكر', 'دعاء']
  const foundTags = commonTags.filter((tag) => aiResponse.includes(tag))

  // Ensure we always have at least some tags
  if (foundTags.length === 0) {
    return ['تأمل', 'تدبر'] // Default tags if none found
  }

  return foundTags
}

function extractExplanation(aiResponse: string): string {
  // Try multiple patterns to extract the explanation section
  let explanationMatch = aiResponse.match(/## الشرح والتفسير\n([\s\S]*?)(?=\n## التطبيق العملي|$)/)

  if (!explanationMatch) {
    explanationMatch = aiResponse.match(/الشرح والتفسير[:\n]\s*([\s\S]*?)(?=\n#|التطبيق العملي|$)/)
  }

  if (!explanationMatch) {
    explanationMatch = aiResponse.match(/الشرح[:\n]\s*([\s\S]*?)(?=\n#|التطبيق|$)/)
  }

  if (!explanationMatch) {
    // Try to find any content between "تفسير" and "تطبيق"
    explanationMatch = aiResponse.match(/تفسير[:\s]*([\s\S]*?)(?=تطبيق|التطبيق|$)/)
  }

  if (!explanationMatch) {
    // Look for content after "النص الكريم" section
    explanationMatch = aiResponse.match(/## النص الكريم[\s\S]*?\n\n([\s\S]*?)(?=\n##|$)/)
  }

  const explanation = explanationMatch ? explanationMatch[1].trim() : ''

  if (!explanation || explanation.length < 50) {
    // More intelligent fallback: try to extract meaningful content
    const lines = aiResponse
      .split('\n')
      .filter(
        (line) =>
          line.trim().length > 20 &&
          !line.includes('##') &&
          !line.includes('**') &&
          !line.includes('سورة') &&
          !line.includes('آية')
      )

    if (lines.length > 0) {
      const intelligentFallback = lines.slice(0, 3).join('\n\n')
      console.warn('تحذير: فشل في استخراج الشرح، سيتم استخدام نص احتياطي ذكي')
      return intelligentFallback
    }

    // Last resort fallback
    const fallbackExplanation = aiResponse.substring(0, 300) + '...'
    console.warn('تحذير: فشل في استخراج الشرح، سيتم استخدام نص احتياطي')
    return fallbackExplanation
  }

  return explanation
}

function extractPracticalPoint(aiResponse: string, index: number): string {
  // Extract practical application points
  const practicalMatch = aiResponse.match(
    /### كيف نطبق هذا اليوم؟\n1\. \*\*في المواقف اليومية\*\*: ([\s\S]*?)2\. \*\*في العبادات\*\*: ([\s\S]*?)3\. \*\*في العلاقات\*\*: ([\s\S]*?)##/
  )

  if (practicalMatch) {
    switch (index) {
      case 1:
        return practicalMatch[1].trim()
      case 2:
        return practicalMatch[2].trim()
      case 3:
        return practicalMatch[3].trim()
    }
  }

  return `تطبيق عملي ${index}`
}

function extractReflectionPoint(aiResponse: string, index: number): string {
  // Extract reflection points
  const reflectionMatch = aiResponse.match(
    /## للتأمل والتفكر\n- ([\s\S]*?)\n- ([\s\S]*?)\n- ([\s\S]*?)$/
  )

  if (reflectionMatch) {
    switch (index) {
      case 1:
        return reflectionMatch[1].trim()
      case 2:
        return reflectionMatch[2].trim()
      case 3:
        return reflectionMatch[3].trim()
    }
  }

  return `سؤال للتأمل ${index}`
}

// Helper function to extract all practical applications as a single text
function extractPracticalApplications(aiResponse: string): string {
  const practical1 = extractPracticalPoint(aiResponse, 1)
  const practical2 = extractPracticalPoint(aiResponse, 2)
  const practical3 = extractPracticalPoint(aiResponse, 3)

  // Ensure we have meaningful content
  const applications = [practical1, practical2, practical3].filter(
    (app) => app && !app.includes('تطبيق عملي') && app.length > 20
  )

  if (applications.length === 0) {
    return `**في المواقف اليومية**: تذكر هذه الآية عند اتخاذ القرارات المهمة\n\n**في العبادات**: استخدم معاني هذه الآية في تعميق خشوعك\n\n**في العلاقات**: طبق مبادئ هذه الآية في تعاملك مع الآخرين`
  }

  return `**في المواقف اليومية**: ${applications[0] || 'تذكر هذه الآية في حياتك اليومية'}\n\n**في العبادات**: ${applications[1] || 'استخدم معاني هذه الآية في عباداتك'}\n\n**في العلاقات**: ${applications[2] || 'طبق مبادئ هذه الآية في علاقاتك'}`
}

// Helper function to extract all reflection questions as a single text
function extractReflectionQuestions(aiResponse: string): string {
  const reflection1 = extractReflectionPoint(aiResponse, 1)
  const reflection2 = extractReflectionPoint(aiResponse, 2)
  const reflection3 = extractReflectionPoint(aiResponse, 3)

  // Ensure we have meaningful questions
  const questions = [reflection1, reflection2, reflection3].filter(
    (q) => q && !q.includes('سؤال للتأمل') && q.length > 10
  )

  if (questions.length === 0) {
    return `• كيف يمكنني تطبيق معاني هذه الآية في حياتي؟\n• ما الدروس العملية التي أستفيدها من هذه الآية؟\n• كيف تؤثر هذه الآية على سلوكي وأفكاري؟`
  }

  return `• ${questions[0] || 'كيف يمكنني الاستفادة من هذه الآية؟'}\n• ${questions[1] || 'ما الدروس التي تعلمني إياها هذه الآية؟'}\n• ${questions[2] || 'كيف أطبق معانيها في حياتي؟'}`
}

// Function to notify admin about new pending article
async function notifyAdmin(article: any) {
  try {
    const notificationManager = new NotificationManager()

    const success = await notificationManager.sendAdminNotification({
      title: article.title,
      surah: article.surah,
      ayah: article.ayah,
      slug: article.slug,
      date: article.date,
    })

    if (!success) {
      throw new Error('Failed to send admin notification')
    }

    console.log(`✅ Admin notification sent successfully for article: ${article.title}`)
  } catch (error) {
    console.error('Error sending admin notification:', error)
    throw error // Re-throw to let caller handle
  }
}

// Generate a verse article
export async function POST(request: NextRequest) {
  try {
    // Generate the article using the AI model
    const article = await generateVerseArticle()

    // Save the verse to database instead of creating a file
    const verseData = {
      title: article.title,
      surah: article.surah,
      ayah: article.ayah,
      slug: article.slug,
      verse_text: article.verseText,
      explanation: extractExplanation(article.aiResponse),
      practical_application: extractPracticalApplications(article.aiResponse),
      reflection_questions: extractReflectionQuestions(article.aiResponse),
      tags: article.tags,
      status: 'pending' as const,
      date: article.date,
    }

    // Validate the verse data before saving to database
    console.log('🔍 التحقق من صحة بيانات الآية...')
    const validation = validateVerseData(verseData)

    if (!validation.isValid) {
      console.error('❌ فشل في التحقق من صحة البيانات:', validation.errors)
      throw new Error(`فشل في التحقق من صحة البيانات: ${validation.errors.join(', ')}`)
    }

    console.log('✅ تم التحقق من صحة البيانات بنجاح')

    const createdVerse = await supabaseVerseManager.createVerse(verseData)

    if (!createdVerse) {
      throw new Error('Failed to save verse to database')
    }

    // Notify admin about the new pending verse
    try {
      await notifyAdmin({
        title: createdVerse.title,
        surah: createdVerse.surah,
        ayah: createdVerse.ayah,
        slug: createdVerse.slug,
        date: createdVerse.date,
      })
    } catch (notificationError) {
      console.error('Failed to notify admin:', notificationError)
      // Don't fail the entire request if notification fails
    }

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء مقال جديد للآية بنجاح وحفظه في قاعدة البيانات',
      verse: {
        id: createdVerse.id,
        title: createdVerse.title,
        slug: createdVerse.slug,
      },
    })
  } catch (error) {
    console.error('Error generating verse article:', error)
    return NextResponse.json(
      {
        success: false,
        message:
          'فشل في إنشاء المقال - ' + (error instanceof Error ? error.message : 'خطأ غير معروف'),
      },
      { status: 500 }
    )
  }
}

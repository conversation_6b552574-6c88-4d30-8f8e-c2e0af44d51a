import { NextRequest, NextResponse } from 'next/server'
import { supabaseSubscriptionManager } from '@/lib/supabase'
import { EmailService } from '@/lib/email-service'
import { z } from 'zod'

// Validation schemas
const subscribeSchema = z.object({
  email: z.string().email('Invalid email address'),
  frequency: z.enum(['daily', 'weekly']),
  language: z.enum(['ar', 'en']).optional().default('ar'),
})

const updatePreferencesSchema = z.object({
  email: z.string().email('Invalid email address'),
  frequency: z.enum(['daily', 'weekly']).optional(),
  language: z.enum(['ar', 'en']).optional(),
  isActive: z.boolean().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const action = body.action || 'subscribe'

    const emailService = new EmailService()

    switch (action) {
      case 'subscribe': {
        const { email, frequency, language } = subscribeSchema.parse(body)

        // Check if already subscribed
        const existing = await supabaseSubscriptionManager.getSubscriptionByEmail(email)
        if (existing) {
          if (existing.isConfirmed) {
            return NextResponse.json({ error: 'Email already subscribed' }, { status: 400 })
          } else {
            // Regenerate confirmation token and resend email
            const tokenRegenerated =
              await supabaseSubscriptionManager.regenerateConfirmationToken(email)
            if (tokenRegenerated) {
              // Get the updated subscription with new token
              const updatedSubscription =
                await supabaseSubscriptionManager.getSubscriptionByEmail(email)
              if (updatedSubscription) {
                const success = await emailService.sendConfirmationEmail(updatedSubscription)
                return NextResponse.json({
                  message:
                    'You already have a pending subscription. A new confirmation email has been sent to your inbox.',
                  success,
                })
              }
            }
            return NextResponse.json(
              { error: 'Failed to resend confirmation email' },
              { status: 500 }
            )
          }
        }

        // Create new subscription or get existing unconfirmed one
        let subscription
        try {
          subscription = await supabaseSubscriptionManager.createSubscription(
            email,
            frequency,
            language
          )
        } catch (error) {
          if (error instanceof Error && error.message.includes('Email already subscribed')) {
            // This error is only thrown for confirmed subscriptions
            return NextResponse.json({ error: 'Email already subscribed' }, { status: 400 })
          }
          throw error
        }

        // Send confirmation email
        const emailSent = await emailService.sendConfirmationEmail(subscription)

        const isExisting =
          (await supabaseSubscriptionManager.getSubscriptionByEmail(email)) !== null
        return NextResponse.json({
          message: isExisting
            ? 'You already have a pending subscription. A new confirmation email has been sent to your inbox.'
            : 'Subscription created. Please check your email to confirm.',
          success: true,
          emailSent,
        })
      }

      case 'update_preferences': {
        const { email, ...updates } = updatePreferencesSchema.parse(body)

        const success = await supabaseSubscriptionManager.updateSubscription(email, updates)

        if (!success) {
          return NextResponse.json({ error: 'Subscription not found' }, { status: 404 })
        }

        return NextResponse.json({
          message: 'Preferences updated successfully',
          success: true,
        })
      }

      case 'get_preferences': {
        const { email } = z.object({ email: z.string().email() }).parse(body)

        const subscription = await supabaseSubscriptionManager.getSubscriptionByEmail(email)

        if (!subscription) {
          return NextResponse.json({ error: 'Subscription not found' }, { status: 404 })
        }

        return NextResponse.json({
          subscription: {
            email: subscription.email,
            frequency: subscription.frequency,
            language: subscription.language,
            isActive: subscription.isActive,
            isConfirmed: subscription.isConfirmed,
            subscribedAt: subscription.subscribedAt,
          },
        })
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Newsletter API error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      )
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'stats': {
        const stats = await supabaseSubscriptionManager.getSubscriptionStats()
        return NextResponse.json({ stats })
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Newsletter API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

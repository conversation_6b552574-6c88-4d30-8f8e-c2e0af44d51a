import { NextRequest, NextResponse } from 'next/server'
import { getBaseUrl } from '@/lib/environment'

export async function POST(request: NextRequest) {
  try {
    // Verify the request is authorized (you might want to add webhook signature verification)
    const authHeader = request.headers.get('authorization')
    const expectedAuth = process.env.WEBHOOK_SECRET || process.env.CRON_SECRET || 'your-secret-key'

    if (authHeader !== `Bearer ${expectedAuth}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { verseSlug } = body

    if (!verseSlug) {
      return NextResponse.json({ error: 'Verse slug is required' }, { status: 400 })
    }

    // Call the email notification API to send new verse notifications
    const baseUrl = getBaseUrl()

    // Prepare headers with Vercel bypass token if available
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Authorization: `Bear<PERSON> ${expectedAuth}`,
    }

    // Add Vercel deployment protection bypass token if available
    if (process.env.VERCEL_AUTOMATION_BYPASS_SECRET) {
      headers['x-vercel-protection-bypass'] = process.env.VERCEL_AUTOMATION_BYPASS_SECRET
      console.log('🔑 Added Vercel bypass token to notification request')
    } else {
      console.warn('⚠️ VERCEL_AUTOMATION_BYPASS_SECRET not found - request may fail with 401')
    }

    const response = await fetch(`${baseUrl}/api/email/notify`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        type: 'new_verse',
        verseSlug,
      }),
    })

    const result = await response.json()

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: 'New verse notifications sent successfully',
        ...result,
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to send new verse notifications', details: result },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('New verse webhook error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

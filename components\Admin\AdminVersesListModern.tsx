'use client'

import { useState, useEffect } from 'react'
import {
  BookOpenIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  TagIcon,
  CalendarIcon,
  ArrowPathIcon,
  XMarkIcon,
  DocumentTextIcon,
  LightBulbIcon,
  ChatBubbleLeftEllipsisIcon,
} from '@heroicons/react/24/outline'

interface Verse {
  id: string
  title: string
  surah: string
  ayah: number
  slug: string
  verse_text: string
  explanation: string
  practical_application: string
  reflection_questions: string
  tags: string[]
  status: 'pending' | 'approved' | 'published'
  created_at: string
  date: string
}

interface AdminVersesListProps {
  initialVerses?: Verse[]
}

export default function AdminVersesList({ initialVerses = [] }: AdminVersesListProps) {
  const [verses, setVerses] = useState<Verse[]>(initialVerses)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedVerse, setSelectedVerse] = useState<Verse | null>(null)
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved'>('all')

  // Fetch verses from the API
  const fetchVerses = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/admin/verses', {
        headers: {
          'x-admin-password': '1327',
        },
      })
      const data = await response.json()

      if (data.success) {
        setVerses(data.verses)
      } else {
        setError(data.message)
      }
    } catch (err) {
      setError('حدث خطأ أثناء جلب الآيات')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  // Handle verse action (approve/reject)
  const handleVerseAction = async (verseSlug: string, action: 'approve' | 'reject') => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/admin/verses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-password': '1327',
        },
        body: JSON.stringify({ action, verseId: verseSlug }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        // Refresh the verses list
        await fetchVerses()

        // Close the modal if it's open
        setSelectedVerse(null)

        // Show success message
        console.log('✅ Verse action completed:', data.message)
      } else {
        setError(data.message || 'حدث خطأ غير معروف')
      }
    } catch (err) {
      setError('حدث خطأ أثناء معالجة الآية')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  // Refresh verses on component mount
  useEffect(() => {
    if (initialVerses.length === 0) {
      fetchVerses()
    }
  }, [])

  // Filter verses based on status
  const filteredVerses = verses.filter((verse) => {
    if (filter === 'all') return true
    return verse.status === filter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center gap-1 rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
            <ClockIcon className="h-3 w-3" />
            معلق
          </span>
        )
      case 'approved':
        return (
          <span className="inline-flex items-center gap-1 rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
            <CheckCircleIcon className="h-3 w-3" />
            موافق عليه
          </span>
        )
      case 'published':
        return (
          <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
            <DocumentTextIcon className="h-3 w-3" />
            منشور
          </span>
        )
      default:
        return null
    }
  }

  if (loading && verses.length === 0) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <ArrowPathIcon className="mx-auto h-12 w-12 animate-spin text-blue-500" />
          <p className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
            جاري تحميل الآيات...
          </p>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">يرجى الانتظار</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-6 dark:border-red-800 dark:bg-red-900/20">
        <div className="flex items-start">
          <XCircleIcon className="h-6 w-6 text-red-400" />
          <div className="mr-3 flex-1">
            <h3 className="text-lg font-medium text-red-800 dark:text-red-400">
              خطأ في تحميل البيانات
            </h3>
            <p className="mt-2 text-sm text-red-700 dark:text-red-300">{error}</p>
            <div className="mt-4 flex gap-3">
              <button
                onClick={fetchVerses}
                className="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white hover:bg-red-500"
              >
                إعادة المحاولة
              </button>
              <button
                onClick={() => setError(null)}
                className="rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white hover:bg-gray-500"
              >
                إخفاء
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="rounded-lg border border-white/20 bg-gradient-to-r from-purple-600/80 to-pink-600/80 p-6 text-white backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">إدارة الآيات</h1>
            <p className="mt-2 text-purple-100">مراجعة وإدارة الآيات المعلقة</p>
          </div>
          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold">{verses.length}</div>
              <div className="text-sm text-purple-100">إجمالي الآيات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {verses.filter((v) => v.status === 'pending').length}
              </div>
              <div className="text-sm text-purple-100">معلقة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {verses.filter((v) => v.status === 'approved').length}
              </div>
              <div className="text-sm text-purple-100">موافق عليها</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex rounded-lg border border-white/20 bg-white/10 p-1 backdrop-blur-sm">
            {(['all', 'pending', 'approved'] as const).map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption)}
                className={`rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${
                  filter === filterOption
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : 'text-purple-200 hover:bg-white/10 hover:text-white'
                }`}
              >
                {filterOption === 'all'
                  ? 'الكل'
                  : filterOption === 'pending'
                    ? 'معلقة'
                    : 'موافق عليها'}
              </button>
            ))}
          </div>
          <div className="text-sm text-purple-200">
            عرض {filteredVerses.length} من {verses.length} آية
          </div>
        </div>
        <button
          onClick={fetchVerses}
          disabled={loading}
          className="inline-flex items-center gap-2 rounded-lg border border-white/20 bg-white/10 px-4 py-2 text-sm font-semibold text-purple-200 shadow-sm backdrop-blur-sm transition-all duration-200 hover:bg-white/20 hover:text-white disabled:opacity-50"
        >
          <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'جاري التحديث...' : 'تحديث'}
        </button>
      </div>

      {/* Verses Grid */}
      {filteredVerses.length === 0 ? (
        <div className="rounded-lg border-2 border-dashed border-gray-300 py-16 text-center dark:border-gray-700">
          <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
            {filter === 'all'
              ? 'لا توجد آيات'
              : `لا توجد آيات ${filter === 'pending' ? 'معلقة' : 'موافق عليها'}`}
          </h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {filter === 'all'
              ? 'لم يتم إنشاء أي آيات بعد'
              : `لا توجد آيات ${filter === 'pending' ? 'معلقة للمراجعة' : 'موافق عليها'} حالياً`}
          </p>
        </div>
      ) : (
        <div className="grid gap-6 lg:grid-cols-2">
          {filteredVerses.map((verse) => (
            <div
              key={verse.id}
              className="group relative cursor-pointer overflow-hidden rounded-xl border border-white/20 bg-white/10 shadow-lg backdrop-blur-sm transition-all hover:bg-white/20 hover:shadow-xl"
              onClick={() => setSelectedVerse(verse)}
            >
              {/* Status Badge */}
              <div className="absolute top-4 left-4 z-10">{getStatusBadge(verse.status)}</div>

              {/* Card Content */}
              <div className="p-6">
                <div className="mb-4">
                  <h3 className="line-clamp-2 text-xl font-bold text-white">{verse.title}</h3>
                  <div className="mt-2 flex items-center gap-4 text-sm text-purple-200">
                    <span className="flex items-center gap-1">
                      <BookOpenIcon className="h-4 w-4" />
                      {verse.surah} - آية {verse.ayah}
                    </span>
                    <span className="flex items-center gap-1">
                      <CalendarIcon className="h-4 w-4" />
                      {new Date(verse.created_at).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                </div>

                {/* Verse Text Preview */}
                <div className="mb-4">
                  <p className="font-arabic line-clamp-3 text-lg leading-relaxed text-purple-100">
                    "{verse.verse_text}"
                  </p>
                </div>

                {/* Tags */}
                <div className="mb-4 flex flex-wrap gap-2">
                  {verse.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 rounded-full border border-purple-400/30 bg-purple-500/20 px-2.5 py-1 text-xs font-medium text-purple-200"
                    >
                      <TagIcon className="h-3 w-3" />
                      {tag}
                    </span>
                  ))}
                  {verse.tags.length > 3 && (
                    <span className="inline-flex items-center rounded-full border border-white/20 bg-white/10 px-2.5 py-1 text-xs font-medium text-purple-200 backdrop-blur-sm">
                      +{verse.tags.length - 3} أخرى
                    </span>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      setSelectedVerse(verse)
                    }}
                    className="inline-flex items-center gap-2 rounded-lg border border-white/20 bg-white/10 px-3 py-2 text-sm font-medium text-purple-200 backdrop-blur-sm transition-all duration-200 hover:bg-white/20 hover:text-white"
                  >
                    <EyeIcon className="h-4 w-4" />
                    عرض التفاصيل
                  </button>

                  <div className="flex gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleVerseAction(verse.slug, 'approve')
                      }}
                      disabled={loading}
                      className="inline-flex items-center gap-1 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 px-3 py-2 text-sm font-semibold text-white transition-all duration-200 hover:from-green-400 hover:to-emerald-400 disabled:opacity-50"
                    >
                      <CheckCircleIcon className="h-4 w-4" />
                      موافقة
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleVerseAction(verse.slug, 'reject')
                      }}
                      disabled={loading}
                      className="inline-flex items-center gap-1 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 px-3 py-2 text-sm font-semibold text-white transition-all duration-200 hover:from-red-400 hover:to-pink-400 disabled:opacity-50"
                    >
                      <XCircleIcon className="h-4 w-4" />
                      رفض
                    </button>
                  </div>
                </div>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/0 to-purple-600/0 opacity-0 transition-opacity group-hover:opacity-5"></div>
            </div>
          ))}
        </div>
      )}

      {/* Detailed Verse Modal */}
      {selectedVerse && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm">
          <div className="max-h-[90vh] w-full max-w-4xl overflow-hidden rounded-2xl border border-white/20 bg-gradient-to-br from-purple-900/95 to-pink-900/95 shadow-2xl backdrop-blur-sm">
            {/* Modal Header */}
            <div className="sticky top-0 border-b border-white/20 bg-gradient-to-r from-purple-600/90 to-pink-600/90 px-6 py-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-white/20 p-2 backdrop-blur-sm">
                    <BookOpenIcon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-white">{selectedVerse.title}</h2>
                    <p className="text-sm text-purple-100">
                      {selectedVerse.surah} - آية {selectedVerse.ayah}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  {getStatusBadge(selectedVerse.status)}
                  <button
                    onClick={() => setSelectedVerse(null)}
                    className="rounded-lg p-2 text-purple-200 transition-all duration-200 hover:bg-white/20 hover:text-white"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className="max-h-[calc(90vh-140px)] overflow-y-auto p-6">
              <div className="space-y-8">
                {/* Verse Text */}
                <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur-sm">
                  <div className="mb-4 flex items-center gap-2">
                    <BookOpenIcon className="h-5 w-5 text-purple-300" />
                    <h3 className="text-lg font-semibold text-white">النص الكريم</h3>
                  </div>
                  <p className="font-arabic text-center text-xl leading-relaxed text-purple-100">
                    "{selectedVerse.verse_text}"
                  </p>
                </div>

                {/* Explanation */}
                <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur-sm">
                  <div className="mb-4 flex items-center gap-2">
                    <DocumentTextIcon className="h-5 w-5 text-green-300" />
                    <h3 className="text-lg font-semibold text-white">الشرح والتفسير</h3>
                  </div>
                  <div className="prose max-w-none text-purple-100">
                    <p className="leading-relaxed whitespace-pre-line">
                      {selectedVerse.explanation}
                    </p>
                  </div>
                </div>

                {/* Practical Application */}
                <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur-sm">
                  <div className="mb-4 flex items-center gap-2">
                    <LightBulbIcon className="h-5 w-5 text-yellow-300" />
                    <h3 className="text-lg font-semibold text-white">التطبيق العملي</h3>
                  </div>
                  <div className="prose max-w-none text-purple-100">
                    <p className="leading-relaxed whitespace-pre-line">
                      {selectedVerse.practical_application}
                    </p>
                  </div>
                </div>

                {/* Reflection Questions */}
                <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur-sm">
                  <div className="mb-4 flex items-center gap-2">
                    <ChatBubbleLeftEllipsisIcon className="h-5 w-5 text-pink-300" />
                    <h3 className="text-lg font-semibold text-white">للتأمل والتفكر</h3>
                  </div>
                  <div className="prose max-w-none text-purple-100">
                    <p className="leading-relaxed whitespace-pre-line">
                      {selectedVerse.reflection_questions}
                    </p>
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <div className="mb-4 flex items-center gap-2">
                    <TagIcon className="h-5 w-5 text-blue-300" />
                    <h3 className="text-lg font-semibold text-white">العلامات</h3>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {selectedVerse.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center gap-1 rounded-full border border-purple-400/30 bg-purple-500/20 px-3 py-1 text-sm font-medium text-purple-200"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="sticky bottom-0 border-t border-white/20 bg-gradient-to-r from-purple-600/90 to-pink-600/90 px-6 py-4 backdrop-blur-sm">
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setSelectedVerse(null)}
                  className="rounded-lg border border-white/20 bg-white/10 px-4 py-2 text-sm font-medium text-purple-200 backdrop-blur-sm transition-all duration-200 hover:bg-white/20 hover:text-white"
                >
                  إغلاق
                </button>
                <button
                  onClick={() => {
                    handleVerseAction(selectedVerse.slug, 'reject')
                  }}
                  disabled={loading}
                  className="inline-flex items-center gap-2 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 px-4 py-2 text-sm font-semibold text-white transition-all duration-200 hover:from-red-400 hover:to-pink-400 disabled:opacity-50"
                >
                  <XCircleIcon className="h-4 w-4" />
                  رفض وحذف
                </button>
                <button
                  onClick={() => {
                    handleVerseAction(selectedVerse.slug, 'approve')
                  }}
                  disabled={loading}
                  className="inline-flex items-center gap-2 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 px-4 py-2 text-sm font-semibold text-white transition-all duration-200 hover:from-green-400 hover:to-emerald-400 disabled:opacity-50"
                >
                  <CheckCircleIcon className="h-4 w-4" />
                  موافقة ونشر
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

# Admin Notification System Documentation

## Overview

The admin notification system ensures that administrators are notified whenever a new Quran verse article is generated and needs review. This document explains how the notification system works and how to configure it.

## How It Works

### 1. Verse Generation Process

When a new verse article is generated (either manually via `/api/generate-verse` or automatically via cron job), the system:

1. Generates the article content using AI
2. Saves the article as a `.mdx` file in the `content/pending-articles` directory
3. **Sends an email notification to the admin**
4. Returns success response

### 2. Admin Notification Flow

```
Generate Verse → Save Article → Notify Admin → Admin Reviews → Approve/Reject
```

### 3. Email Content

The admin notification email includes:

- **Article details**: Title, Surah, Ayah number, date
- **Direct link** to the admin dashboard for review
- **Professional HTML formatting** with clear call-to-action
- **Arabic/English bilingual support**

## Configuration

### Required Environment Variables

Add these to your `.env.local` file:

```env
# Admin Email (Required)
ADMIN_EMAIL=<EMAIL>

# SMTP Configuration (Required for sending emails)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Deployment URL (for email links)
VERCEL_URL=your_site_url.com
# OR for local development:
NEXTAUTH_URL=http://localhost:3000
```

### SMTP Setup for Gmail

1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account Settings
   - Security → 2-Step Verification → App passwords
   - Create password for "Mail"
3. Use the generated app password as `SMTP_PASS`

## Files Modified

### `/app/api/generate-verse/route.ts`

- Added admin notification call after article generation
- Imports `NotificationManager` from notification utils
- Handles notification errors gracefully

### `/lib/notification-utils.ts`

- Added `sendAdminNotification()` method to `NotificationManager` class
- Professional HTML email template
- Error handling and logging

### `/lib/email-service.ts`

- Added `sendRawEmail()` method for custom email sending
- Supports HTML and plain text emails
- Uses existing SMTP configuration

## Testing

### Manual Test

```bash
# Call the API endpoint directly
curl -X POST http://localhost:3000/api/generate-verse
```

### Verify Email Configuration

```bash
# Check if SMTP credentials are configured
echo $SMTP_USER
echo $SMTP_PASS
echo $ADMIN_EMAIL
```

## Admin Dashboard

After receiving the notification email, administrators can:

1. **Click the review link** in the email
2. **Visit `/admin/verses`** directly
3. **Review the generated content**
4. **Approve** (moves to `/content/verses/`) or **Reject** (deletes file)

## Troubleshooting

### Common Issues

1. **Emails not being sent**
   - Verify SMTP credentials in environment variables
   - Check Gmail app password setup
   - Look for error logs in console

2. **Links in email not working**
   - Verify `VERCEL_URL` or `NEXTAUTH_URL` is set correctly
   - Ensure the admin dashboard is accessible

3. **Admin not receiving emails**
   - Check `ADMIN_EMAIL` environment variable
   - Verify email isn't going to spam folder
   - Check email service logs

### Debug Mode

Enable debug logging by checking the console for:

- `✅ Admin notification sent successfully`
- `❌ Failed to send admin notification email`
- SMTP configuration details

## Security Considerations

1. **Environment Variables**: Never commit `.env` files with real credentials
2. **Email Validation**: Admin email should be validated
3. **Rate Limiting**: Consider adding rate limits to verse generation
4. **Access Control**: Admin dashboard should have authentication

## Future Enhancements

1. **Multiple Admin Emails**: Support notifying multiple administrators
2. **Email Templates**: More sophisticated email designs
3. **Push Notifications**: Browser/mobile push notifications
4. **Slack/Discord Integration**: Alternative notification channels
5. **Email Preferences**: Admin can configure notification preferences

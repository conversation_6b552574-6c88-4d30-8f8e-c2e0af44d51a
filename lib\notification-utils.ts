import { getAllVerses } from './verses'
import { supabaseSubscriptionManager, supabaseEmailLogger } from './supabase'
import { EmailService } from './email-service'
import { getBaseUrl } from './environment'
import siteMetadata from '@/data/siteMetadata'

// Utility functions for email notifications

export interface NotificationResult {
  success: boolean
  sent: number
  failed: number
  totalSubscriptions: number
  verse?: {
    title: string
    slug: string
  }
  error?: string
}

export class NotificationManager {
  private emailService: EmailService

  constructor() {
    this.emailService = new EmailService()
  }

  // Send notifications for a new verse
  async sendNewVerseNotifications(verseSlug: string): Promise<NotificationResult> {
    try {
      // Get the verse
      const allVerses = getAllVerses()
      const verse = allVerses.find((v) => v.slug === verseSlug)

      if (!verse) {
        return {
          success: false,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          error: 'Verse not found',
        }
      }

      // Get daily subscribers (new verses go to daily subscribers)
      const subscriptions =
        await supabaseSubscriptionManager.getActiveSubscriptionsByFrequency('daily')

      if (subscriptions.length === 0) {
        return {
          success: true,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          verse: {
            title: verse.title,
            slug: verse.slug,
          },
        }
      }

      // Prepare verse data for email
      const verseEmailData = {
        title: verse.title,
        surah: verse.surah,
        ayah: verse.ayah,
        slug: verse.slug,
        lesson: verse.lesson,
        reflection: verse.reflection,
        url: `${getBaseUrl()}/verses/${verse.slug}`,
      }

      // Send emails
      let successCount = 0
      let failureCount = 0

      for (const subscription of subscriptions) {
        try {
          const success = await this.emailService.sendVerseNotification(
            subscription,
            verseEmailData,
            'new_verse'
          )

          if (success) {
            successCount++
            await supabaseSubscriptionManager.updateLastEmailSent(subscription.id)
          } else {
            failureCount++
          }

          // Add delay to avoid overwhelming email service
          await new Promise((resolve) => setTimeout(resolve, 100))
        } catch (error) {
          console.error(`Failed to send email to ${subscription.email}:`, error)
          failureCount++
        }
      }

      return {
        success: true,
        sent: successCount,
        failed: failureCount,
        totalSubscriptions: subscriptions.length,
        verse: {
          title: verse.title,
          slug: verse.slug,
        },
      }
    } catch (error) {
      console.error('Error sending new verse notifications:', error)
      return {
        success: false,
        sent: 0,
        failed: 0,
        totalSubscriptions: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // Send daily reminder notifications
  async sendDailyReminders(): Promise<NotificationResult> {
    try {
      // Get today's verse
      const { getTodaysVerse } = await import('./verses')
      const verse = getTodaysVerse()

      if (!verse) {
        return {
          success: false,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          error: 'No verse available for today',
        }
      }

      // Get daily subscribers
      const subscriptions =
        await supabaseSubscriptionManager.getActiveSubscriptionsByFrequency('daily')

      if (subscriptions.length === 0) {
        return {
          success: true,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          verse: {
            title: verse.title,
            slug: verse.slug,
          },
        }
      }

      // Filter out subscribers who already received an email today
      const today = new Date().toISOString().split('T')[0]
      const subscribersToEmail = subscriptions.filter((sub) => {
        if (!sub.lastEmailSent) return true
        const lastEmailDate = new Date(sub.lastEmailSent).toISOString().split('T')[0]
        return lastEmailDate !== today
      })

      // Prepare verse data for email
      const verseEmailData = {
        title: verse.title,
        surah: verse.surah,
        ayah: verse.ayah,
        slug: verse.slug,
        lesson: verse.lesson,
        reflection: verse.reflection,
        url: `${getBaseUrl()}/verses/${verse.slug}`,
      }

      // Send emails
      let successCount = 0
      let failureCount = 0

      for (const subscription of subscribersToEmail) {
        try {
          const success = await this.emailService.sendVerseNotification(
            subscription,
            verseEmailData,
            'daily_reminder'
          )

          if (success) {
            successCount++
            await supabaseSubscriptionManager.updateLastEmailSent(subscription.id)
          } else {
            failureCount++
          }

          // Add delay to avoid overwhelming email service
          await new Promise((resolve) => setTimeout(resolve, 100))
        } catch (error) {
          console.error(`Failed to send email to ${subscription.email}:`, error)
          failureCount++
        }
      }

      return {
        success: true,
        sent: successCount,
        failed: failureCount,
        totalSubscriptions: subscriptions.length,
        verse: {
          title: verse.title,
          slug: verse.slug,
        },
      }
    } catch (error) {
      console.error('Error sending daily reminders:', error)
      return {
        success: false,
        sent: 0,
        failed: 0,
        totalSubscriptions: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // Send weekly reminder notifications
  async sendWeeklyReminders(): Promise<NotificationResult> {
    try {
      // Get today's verse for weekly reminder
      const { getTodaysVerse } = await import('./verses')
      const verse = getTodaysVerse()

      if (!verse) {
        return {
          success: false,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          error: 'No verse available for weekly reminder',
        }
      }

      // Get weekly subscribers
      const subscriptions =
        await supabaseSubscriptionManager.getActiveSubscriptionsByFrequency('weekly')

      if (subscriptions.length === 0) {
        return {
          success: true,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          verse: {
            title: verse.title,
            slug: verse.slug,
          },
        }
      }

      // Filter out subscribers who already received an email this week
      const now = new Date()
      const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))
      const weekStartStr = weekStart.toISOString().split('T')[0]

      const subscribersToEmail = subscriptions.filter((sub) => {
        if (!sub.lastEmailSent) return true
        const lastEmailDate = new Date(sub.lastEmailSent).toISOString().split('T')[0]
        return lastEmailDate < weekStartStr
      })

      // Prepare verse data for email
      const verseEmailData = {
        title: verse.title,
        surah: verse.surah,
        ayah: verse.ayah,
        slug: verse.slug,
        lesson: verse.lesson,
        reflection: verse.reflection,
        url: `${getBaseUrl()}/verses/${verse.slug}`,
      }

      // Send emails
      let successCount = 0
      let failureCount = 0

      for (const subscription of subscribersToEmail) {
        try {
          const success = await this.emailService.sendVerseNotification(
            subscription,
            verseEmailData,
            'weekly_reminder'
          )

          if (success) {
            successCount++
            await supabaseSubscriptionManager.updateLastEmailSent(subscription.id)
          } else {
            failureCount++
          }

          // Add delay to avoid overwhelming email service
          await new Promise((resolve) => setTimeout(resolve, 100))
        } catch (error) {
          console.error(`Failed to send email to ${subscription.email}:`, error)
          failureCount++
        }
      }

      return {
        success: true,
        sent: successCount,
        failed: failureCount,
        totalSubscriptions: subscriptions.length,
        verse: {
          title: verse.title,
          slug: verse.slug,
        },
      }
    } catch (error) {
      console.error('Error sending weekly reminders:', error)
      return {
        success: false,
        sent: 0,
        failed: 0,
        totalSubscriptions: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // Get notification statistics
  async getNotificationStats() {
    return await supabaseSubscriptionManager.getSubscriptionStats()
  }

  // Get recent email logs
  async getRecentEmailLogs(limit: number = 50) {
    return await supabaseEmailLogger.getEmailLogs(undefined, limit)
  }

  // Send admin notification about new pending article
  async sendAdminNotification(article: {
    title: string
    surah: string
    ayah: number
    slug: string
    date: string
  }): Promise<boolean> {
    try {
      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
      const baseUrl = getBaseUrl()

      const adminDashboardUrl = `https://quran-verses-gilt.vercel.app/admin/verses`

      const emailContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #2c3e50; margin-top: 0;">🆕 مقال جديد للآية ينتظر الموافقة</h2>
            
            <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3498db;">
              <h3 style="margin-top: 0; color: #2980b9;">تفاصيل المقال:</h3>
              <p><strong>العنوان:</strong> ${article.title}</p>
              <p><strong>السورة:</strong> ${article.surah}</p>
              <p><strong>رقم الآية:</strong> ${article.ayah}</p>
              <p><strong>التاريخ:</strong> ${article.date}</p>
              <p><strong>الملف:</strong> ${article.slug}.mdx</p>
            </div>

            <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
              <h3 style="margin-top: 0; color: #856404;">📝 إجراءات مطلوبة:</h3>
              <p>تم إنشاء مقال جديد للآية وحفظه في مجلد المقالات المعلقة. يرجى مراجعة المحتوى والموافقة عليه أو رفضه.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${adminDashboardUrl}" 
                 style="display: inline-block; background-color: #3498db; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                📋 مراجعة المقال الآن
              </a>
            </div>

            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
              <p style="margin: 0; font-size: 14px; color: #6c757d;">
                <strong>ملاحظة:</strong> يمكنك الوصول لوحة التحكم الإدارية في أي وقت عبر: 
                <a href="${adminDashboardUrl}" style="color: #3498db;">${adminDashboardUrl}</a>
              </p>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #6c757d; font-size: 12px;">
            <p>© ${new Date().getFullYear()} ${siteMetadata.title} | نظام إشعارات تلقائي</p>
          </div>
        </div>
      `

      const success = await this.emailService.sendRawEmail({
        to: adminEmail,
        subject: `🆕 مقال جديد للآية: ${article.title}`,
        html: emailContent,
      })

      if (success) {
        console.log(`✅ Admin notification sent successfully for article: ${article.title}`)
        return true
      } else {
        console.error('❌ Failed to send admin notification email')
        return false
      }
    } catch (error) {
      console.error('Error sending admin notification:', error)
      return false
    }
  }

  // Send admin notification for new subscriber
  async sendNewSubscriberNotification(
    subscriberEmail: string,
    frequency: string,
    language: string
  ): Promise<boolean> {
    try {
      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
      const adminDashboardUrl = `https://quran-verses-gilt.vercel.app/admin/email-subscriptions`

      const frequencyText = frequency === 'daily' ? 'يومي' : 'أسبوعي'
      const languageText = language === 'ar' ? 'العربية' : 'English'

      const emailContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #2c3e50; margin-top: 0;">🎉 مشترك جديد في النشرة البريدية</h2>
            
            <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
              <h3 style="margin-top: 0; color: #155724;">تفاصيل المشترك الجديد:</h3>
              <p><strong>البريد الإلكتروني:</strong> ${subscriberEmail}</p>
              <p><strong>تكرار الإرسال:</strong> ${frequencyText}</p>
              <p><strong>اللغة المفضلة:</strong> ${languageText}</p>
              <p><strong>تاريخ الاشتراك:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>

            <div style="background-color: #cce5ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;">
              <h3 style="margin-top: 0; color: #004085;">📊 معلومات إضافية:</h3>
              <p>تم تأكيد الاشتراك بنجاح وسيبدأ المشترك في تلقي الآيات حسب التكرار المحدد.</p>
              <p>يمكنك مراجعة جميع المشتركين وإدارة قائمة البريد الإلكتروني من لوحة التحكم.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${adminDashboardUrl}" 
                 style="display: inline-block; background-color: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                📧 إدارة المشتركين
              </a>
            </div>

            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
              <p style="margin: 0; font-size: 14px; color: #6c757d;">
                <strong>ملاحظة:</strong> يمكنك الوصول لإدارة المشتركين في أي وقت عبر: 
                <a href="${adminDashboardUrl}" style="color: #007bff;">${adminDashboardUrl}</a>
              </p>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #6c757d; font-size: 12px;">
            <p>© ${new Date().getFullYear()} ${siteMetadata.title} | نظام إشعارات تلقائي</p>
          </div>
        </div>
      `

      const success = await this.emailService.sendRawEmail({
        to: adminEmail,
        subject: `🎉 مشترك جديد: ${subscriberEmail}`,
        html: emailContent,
      })

      if (success) {
        console.log(
          `✅ Admin notification sent successfully for new subscriber: ${subscriberEmail}`
        )
        return true
      } else {
        console.error('❌ Failed to send admin notification email for new subscriber')
        return false
      }
    } catch (error) {
      console.error('Error sending new subscriber notification:', error)
      return false
    }
  }
}

import { createClient } from '@supabase/supabase-js'
import { v4 as uuidv4 } from 'uuid'

// Create a single supabase client for interacting with your database
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Database types matching our current schema but for Supabase
export interface EmailSubscription {
  id: string
  email: string
  frequency: 'daily' | 'weekly'
  is_active: boolean
  language: 'ar' | 'en'
  subscribed_at: string
  last_email_sent?: string
  unsubscribe_token: string
  confirmation_token?: string
  is_confirmed: boolean
}

export interface EmailLog {
  id: string
  subscription_id: string
  verse_slug: string
  sent_at: string
  email_type: 'new_verse' | 'daily_reminder' | 'weekly_reminder'
  success: boolean
  error_message?: string
}

// Verse interface for database storage
export interface Verse {
  id: string
  title: string
  surah: string
  ayah: number
  slug: string
  verse_text: string
  explanation: string
  practical_application: string
  reflection_questions: string
  tags: string[]
  status: 'pending' | 'approved' | 'published'
  created_at: string
  updated_at: string
  date: string
}

// Legacy interface for backward compatibility
export interface LegacyEmailSubscription {
  id: string
  email: string
  frequency: 'daily' | 'weekly'
  isActive: boolean
  language: 'ar' | 'en'
  subscribedAt: string
  lastEmailSent?: string
  unsubscribeToken: string
  confirmationToken?: string
  isConfirmed: boolean
}

export interface LegacyEmailLog {
  id: string
  subscriptionId: string
  verseSlug: string
  sentAt: string
  emailType: 'new_verse' | 'daily_reminder' | 'weekly_reminder'
  success: boolean
  errorMessage?: string
}

// Helper function to convert Supabase format to legacy format
function convertToLegacy(subscription: EmailSubscription): LegacyEmailSubscription {
  return {
    id: subscription.id,
    email: subscription.email,
    frequency: subscription.frequency,
    isActive: subscription.is_active,
    language: subscription.language,
    subscribedAt: subscription.subscribed_at,
    lastEmailSent: subscription.last_email_sent,
    unsubscribeToken: subscription.unsubscribe_token,
    confirmationToken: subscription.confirmation_token,
    isConfirmed: subscription.is_confirmed,
  }
}

// Helper function to convert legacy format to Supabase format
function convertFromLegacy(
  subscription: Partial<LegacyEmailSubscription>
): Partial<EmailSubscription> {
  const converted: Partial<EmailSubscription> = {}

  if (subscription.id) converted.id = subscription.id
  if (subscription.email) converted.email = subscription.email
  if (subscription.frequency) converted.frequency = subscription.frequency
  if (subscription.isActive !== undefined) converted.is_active = subscription.isActive
  if (subscription.language) converted.language = subscription.language
  if (subscription.subscribedAt) converted.subscribed_at = subscription.subscribedAt
  if (subscription.lastEmailSent) converted.last_email_sent = subscription.lastEmailSent
  if (subscription.unsubscribeToken) converted.unsubscribe_token = subscription.unsubscribeToken
  if (subscription.confirmationToken) converted.confirmation_token = subscription.confirmationToken
  if (subscription.isConfirmed !== undefined) converted.is_confirmed = subscription.isConfirmed

  return converted
}

// Helper function to convert Supabase email log to legacy format
function convertEmailLogToLegacy(log: EmailLog): LegacyEmailLog {
  return {
    id: log.id,
    subscriptionId: log.subscription_id,
    verseSlug: log.verse_slug,
    sentAt: log.sent_at,
    emailType: log.email_type,
    success: log.success,
    errorMessage: log.error_message,
  }
}

// Supabase Subscription Manager (backward compatible with the old API)
export class SupabaseSubscriptionManager {
  // Create a new subscription or return existing unconfirmed one
  async createSubscription(
    email: string,
    frequency: 'daily' | 'weekly',
    language: 'ar' | 'en' = 'ar'
  ): Promise<LegacyEmailSubscription> {
    const subscription: EmailSubscription = {
      id: uuidv4(),
      email: email.toLowerCase().trim(),
      frequency,
      is_active: true,
      language,
      subscribed_at: new Date().toISOString(),
      unsubscribe_token: uuidv4(),
      confirmation_token: uuidv4(),
      is_confirmed: false,
    }

    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .insert([subscription])
        .select()
        .single()

      if (error) {
        if (error.code === '23505') {
          // Unique constraint violation
          // Check if there's an existing unconfirmed subscription
          const existing = await this.getSubscriptionByEmail(email)
          if (existing && !existing.isConfirmed) {
            // Regenerate the confirmation token and return the existing subscription
            await this.regenerateConfirmationToken(email)
            return existing
          }
          throw new Error('Email already subscribed')
        }
        throw error
      }

      return convertToLegacy(data as EmailSubscription)
    } catch (error) {
      console.error('Error creating subscription:', error)
      throw error
    }
  }

  // Get subscription by email
  async getSubscriptionByEmail(email: string): Promise<LegacyEmailSubscription | null> {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('email', email.toLowerCase().trim())
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // No rows found
        }
        throw error
      }

      return convertToLegacy(data as EmailSubscription)
    } catch (error) {
      console.error('Error getting subscription by email:', error)
      return null
    }
  }

  // Get subscription by unsubscribe token
  async getSubscriptionByUnsubscribeToken(token: string): Promise<LegacyEmailSubscription | null> {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('unsubscribe_token', token)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // No rows found
        }
        throw error
      }

      return convertToLegacy(data as EmailSubscription)
    } catch (error) {
      console.error('Error getting subscription by unsubscribe token:', error)
      return null
    }
  }

  // Get subscription by confirmation token
  async getSubscriptionByConfirmationToken(token: string): Promise<LegacyEmailSubscription | null> {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('confirmation_token', token)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // No rows found
        }
        throw error
      }

      return convertToLegacy(data as EmailSubscription)
    } catch (error) {
      console.error('Error getting subscription by confirmation token:', error)
      return null
    }
  }

  // Confirm subscription
  async confirmSubscription(token: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('subscriptions')
        .update({
          is_confirmed: true,
          confirmation_token: null,
        })
        .eq('confirmation_token', token)

      if (error) {
        console.error('Error confirming subscription:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error confirming subscription:', error)
      return false
    }
  }

  // Enhanced confirmation method that returns more information
  async confirmSubscriptionWithDetails(token: string): Promise<{
    success: boolean
    alreadyConfirmed: boolean
    found: boolean
    subscription?: LegacyEmailSubscription
  }> {
    try {
      // First, try to find the subscription with this token
      const subscription = await this.getSubscriptionByConfirmationToken(token)
      console.log('Token lookup result:', subscription ? 'found' : 'not found')

      if (!subscription) {
        // Check if there's any confirmed subscription (might have been confirmed already)
        const hasConfirmed = await this.hasConfirmedSubscriptionLikely()
        console.log('Has confirmed subscriptions:', hasConfirmed)
        return {
          success: false,
          alreadyConfirmed: hasConfirmed,
          found: false,
        }
      }

      if (subscription.isConfirmed) {
        console.log('Subscription already confirmed')
        return {
          success: true,
          alreadyConfirmed: true,
          found: true,
          subscription,
        }
      }

      // Attempt to confirm
      console.log('Attempting to confirm subscription')
      const confirmResult = await this.confirmSubscription(token)
      console.log('Confirmation result:', confirmResult)
      return {
        success: confirmResult,
        alreadyConfirmed: false,
        found: true,
        subscription,
      }
    } catch (error) {
      console.error('Error in confirmSubscriptionWithDetails:', error)
      return {
        success: false,
        alreadyConfirmed: false,
        found: false,
      }
    }
  }

  // Regenerate confirmation token for unconfirmed subscriptions
  async regenerateConfirmationToken(email: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('subscriptions')
        .update({
          confirmation_token: uuidv4(),
          is_confirmed: false,
          last_email_sent: new Date().toISOString(),
        })
        .eq('email', email.toLowerCase().trim())
        .eq('is_confirmed', false)

      if (error) {
        console.error('Error regenerating confirmation token:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error regenerating confirmation token:', error)
      return false
    }
  }

  // Check if any subscription exists (for handling expired/used tokens)
  async hasAnyConfirmedSubscription(): Promise<boolean> {
    try {
      const { count, error } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('is_confirmed', true)

      if (error) {
        console.error('Error checking confirmed subscriptions:', error)
        return false
      }

      return (count || 0) > 0
    } catch (error) {
      console.error('Error checking confirmed subscriptions:', error)
      return false
    }
  }

  // Check if there's a confirmed subscription that might have used this token
  async hasConfirmedSubscriptionLikely(): Promise<boolean> {
    try {
      const { count, error } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('is_confirmed', true)
        .is('confirmation_token', null)

      if (error) {
        console.error('Error checking likely confirmed subscriptions:', error)
        return false
      }

      console.log('Confirmed subscriptions count:', count || 0)
      return (count || 0) > 0
    } catch (error) {
      console.error('Error checking likely confirmed subscriptions:', error)
      return false
    }
  }

  // Update subscription preferences
  async updateSubscription(
    email: string,
    updates: Partial<Pick<LegacyEmailSubscription, 'frequency' | 'language' | 'isActive'>>
  ): Promise<boolean> {
    try {
      const supabaseUpdates = convertFromLegacy(updates)

      const { error } = await supabase
        .from('subscriptions')
        .update(supabaseUpdates)
        .eq('email', email.toLowerCase().trim())

      if (error) {
        console.error('Error updating subscription:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error updating subscription:', error)
      return false
    }
  }

  // Unsubscribe by token
  async unsubscribe(token: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('subscriptions')
        .update({ is_active: false })
        .eq('unsubscribe_token', token)

      if (error) {
        console.error('Error unsubscribing:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error unsubscribing:', error)
      return false
    }
  }

  // Get active subscriptions by frequency
  async getActiveSubscriptionsByFrequency(
    frequency: 'daily' | 'weekly'
  ): Promise<LegacyEmailSubscription[]> {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('is_active', true)
        .eq('is_confirmed', true)
        .eq('frequency', frequency)

      if (error) {
        console.error('Error getting active subscriptions:', error)
        return []
      }

      return (data as EmailSubscription[]).map(convertToLegacy)
    } catch (error) {
      console.error('Error getting active subscriptions:', error)
      return []
    }
  }

  // Update last email sent timestamp
  async updateLastEmailSent(subscriptionId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('subscriptions')
        .update({ last_email_sent: new Date().toISOString() })
        .eq('id', subscriptionId)

      if (error) {
        console.error('Error updating last email sent:', error)
      }
    } catch (error) {
      console.error('Error updating last email sent:', error)
    }
  }

  // Get subscription statistics
  async getSubscriptionStats() {
    try {
      const { count: total, error: totalError } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('is_confirmed', true)

      const { count: active, error: activeError } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)
        .eq('is_confirmed', true)

      const { count: daily, error: dailyError } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('frequency', 'daily')
        .eq('is_active', true)
        .eq('is_confirmed', true)

      const { count: weekly, error: weeklyError } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('frequency', 'weekly')
        .eq('is_active', true)
        .eq('is_confirmed', true)

      if (totalError || activeError || dailyError || weeklyError) {
        console.error('Error getting subscription stats:', {
          totalError,
          activeError,
          dailyError,
          weeklyError,
        })
        return { total: 0, active: 0, daily: 0, weekly: 0 }
      }

      return {
        total: total || 0,
        active: active || 0,
        daily: daily || 0,
        weekly: weekly || 0,
      }
    } catch (error) {
      console.error('Error getting subscription stats:', error)
      return { total: 0, active: 0, daily: 0, weekly: 0 }
    }
  }
}

// Supabase Email Logger (backward compatible with the old API)
export class SupabaseEmailLogger {
  async logEmail(
    subscriptionId: string,
    verseSlug: string,
    emailType: 'new_verse' | 'daily_reminder' | 'weekly_reminder',
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    try {
      const { error } = await supabase.from('email_logs').insert([
        {
          id: uuidv4(),
          subscription_id: subscriptionId,
          verse_slug: verseSlug,
          sent_at: new Date().toISOString(),
          email_type: emailType,
          success,
          error_message: errorMessage,
        },
      ])

      if (error) {
        console.error('Error logging email:', error)
      }
    } catch (error) {
      console.error('Error logging email:', error)
    }
  }

  async getEmailLogs(subscriptionId?: string, limit: number = 100): Promise<LegacyEmailLog[]> {
    try {
      let query = supabase.from('email_logs').select('*')

      if (subscriptionId) {
        query = query.eq('subscription_id', subscriptionId)
      }

      const { data, error } = await query.order('sent_at', { ascending: false }).limit(limit)

      if (error) {
        console.error('Error getting email logs:', error)
        return []
      }

      return (data as EmailLog[]).map(convertEmailLogToLegacy)
    } catch (error) {
      console.error('Error getting email logs:', error)
      return []
    }
  }
}

// Export instances for backward compatibility
export const supabaseSubscriptionManager = new SupabaseSubscriptionManager()
export const supabaseEmailLogger = new SupabaseEmailLogger()

// Verse Management Class
export class SupabaseVerseManager {
  // Create a new verse in the database
  async createVerse(
    verseData: Omit<Verse, 'id' | 'created_at' | 'updated_at'>
  ): Promise<Verse | null> {
    try {
      // Additional validation at database level
      if (!verseData.title || !verseData.surah || !verseData.verse_text || !verseData.explanation) {
        console.error('❌ Missing required fields for verse creation:', {
          hasTitle: !!verseData.title,
          hasSurah: !!verseData.surah,
          hasVerseText: !!verseData.verse_text,
          hasExplanation: !!verseData.explanation,
          hasAyah: !!verseData.ayah && verseData.ayah > 0,
        })
        return null
      }

      const id = uuidv4()
      const now = new Date().toISOString()

      console.log('💾 حفظ الآية في قاعدة البيانات:', {
        title: verseData.title,
        surah: verseData.surah,
        ayah: verseData.ayah,
        slug: verseData.slug,
      })

      const { data, error } = await supabase
        .from('verses')
        .insert([
          {
            id,
            title: verseData.title,
            surah: verseData.surah,
            ayah: verseData.ayah,
            slug: verseData.slug,
            verse_text: verseData.verse_text,
            explanation: verseData.explanation,
            practical_application: verseData.practical_application,
            reflection_questions: verseData.reflection_questions,
            tags: verseData.tags,
            status: verseData.status,
            date: verseData.date,
            created_at: now,
            updated_at: now,
          },
        ])
        .select()
        .single()

      if (error) {
        console.error('❌ Error creating verse in database:', error)
        return null
      }

      console.log('✅ تم حفظ الآية بنجاح في قاعدة البيانات')
      return data as Verse
    } catch (error) {
      console.error('Error creating verse:', error)
      return null
    }
  }

  // Get all verses with optional status filter
  async getVerses(status?: 'pending' | 'approved' | 'published'): Promise<Verse[]> {
    try {
      let query = supabase.from('verses').select('*')

      if (status) {
        query = query.eq('status', status)
      }

      const { data, error } = await query.order('created_at', { ascending: false })

      if (error) {
        console.error('Error getting verses:', error)
        return []
      }

      return data as Verse[]
    } catch (error) {
      console.error('Error getting verses:', error)
      return []
    }
  }

  // Get a single verse by slug
  async getVerseBySlug(slug: string): Promise<Verse | null> {
    try {
      const { data, error } = await supabase.from('verses').select('*').eq('slug', slug).single()

      if (error) {
        console.error('Error getting verse by slug:', error)
        return null
      }

      return data as Verse
    } catch (error) {
      console.error('Error getting verse by slug:', error)
      return null
    }
  }

  // Update verse status (approve/reject)
  async updateVerseStatus(
    id: string,
    status: 'approved' | 'published' | 'rejected'
  ): Promise<boolean> {
    try {
      const updates: any = {
        status,
        updated_at: new Date().toISOString(),
      }

      const { error } = await supabase.from('verses').update(updates).eq('id', id)

      if (error) {
        console.error('Error updating verse status:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error updating verse status:', error)
      return false
    }
  }

  // Delete a verse
  async deleteVerse(id: string): Promise<boolean> {
    try {
      const { error } = await supabase.from('verses').delete().eq('id', id)

      if (error) {
        console.error('Error deleting verse:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error deleting verse:', error)
      return false
    }
  }

  // Get verses by surah
  async getVersesBySurah(surah: string): Promise<Verse[]> {
    try {
      const { data, error } = await supabase
        .from('verses')
        .select('*')
        .eq('surah', surah)
        .eq('status', 'published')
        .order('ayah', { ascending: true })

      if (error) {
        console.error('Error getting verses by surah:', error)
        return []
      }

      return data as Verse[]
    } catch (error) {
      console.error('Error getting verses by surah:', error)
      return []
    }
  }

  // Get verses by tags
  async getVersesByTag(tag: string): Promise<Verse[]> {
    try {
      const { data, error } = await supabase
        .from('verses')
        .select('*')
        .contains('tags', [tag])
        .eq('status', 'published')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error getting verses by tag:', error)
        return []
      }

      return data as Verse[]
    } catch (error) {
      console.error('Error getting verses by tag:', error)
      return []
    }
  }

  // Get random verse
  async getRandomVerse(): Promise<Verse | null> {
    try {
      // First get the count of published verses
      const { count, error: countError } = await supabase
        .from('verses')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'published')

      if (countError || !count) {
        console.error('Error getting verse count:', countError)
        return null
      }

      // Generate a random offset
      const randomOffset = Math.floor(Math.random() * count)

      // Get a random verse
      const { data, error } = await supabase
        .from('verses')
        .select('*')
        .eq('status', 'published')
        .range(randomOffset, randomOffset)

      if (error || !data || data.length === 0) {
        console.error('Error getting random verse:', error)
        return null
      }

      return data[0] as Verse
    } catch (error) {
      console.error('Error getting random verse:', error)
      return null
    }
  }
}

// Export verse manager instance
export const supabaseVerseManager = new SupabaseVerseManager()

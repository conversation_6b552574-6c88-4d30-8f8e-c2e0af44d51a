{"compilerOptions": {"incremental": true, "target": "ES6", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "composite": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/components/*": ["components/*"], "@/data/*": ["data/*"], "@/layouts/*": ["layouts/*"], "@/css/*": ["css/*"], "@/lib/*": ["lib/*"], "@/hooks/*": ["hooks/*"], "@/contexts/*": ["contexts/*"], "@/types/*": ["types/*"], "contentlayer/generated": ["./.contentlayer/generated"], "pliny/*": ["node_modules/pliny/*"]}, "plugins": [{"name": "next"}], "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.js", "**/*.mjs", "**/*.ts", "**/*.tsx", "**/*.json", ".contentlayer/generated", ".contentlayer/generated/**/*.json", ".next/types/**/*.ts"], "exclude": ["node_modules"]}